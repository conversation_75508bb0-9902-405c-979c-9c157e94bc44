# 通用方法迁移示例

## 概述

本文档展示如何将现有的8个Word/PDF工具类迁移到使用 `CommonDocumentUtils` 中的通用方法，以减少代码重复并提高可维护性。

## 已完成的迁移示例

### 1. DynamicInfoWordUtils 迁移示例

#### 迁移前：
```java
public class DynamicInfoWordUtils {
    // 原来的私有方法
    private static InputStream getTemplateInputStream(String templatePath) throws Exception {
        // 重复的模板加载逻辑...
    }
    
    private static void replaceInDocumentBody(XWPFDocument document, Map<String, String> params) {
        // 重复的占位符替换逻辑...
    }
    
    private static void processParagraphsAndTables(XWPFDocument document, Map<String, String> params) {
        // 重复的表格处理逻辑...
    }
    
    public static byte[] generateDynamicInfoDocument(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        try (InputStream templateStream = getTemplateInputStream(templatePath)) {
            XWPFDocument document = new XWPFDocument(templateStream);
            
            Map<String, String> params = buildPlaceholderParams(data);
            replaceInDocumentBody(document, params);
            processParagraphsAndTables(document, params);
            fillDynamicTableData(document, data);
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            return outputStream.toByteArray();
        }
    }
}
```

#### 迁移后：
```java
public class DynamicInfoWordUtils {
    // 模板加载方法已迁移到 CommonDocumentUtils.getTemplateInputStream()
    // 占位符替换方法已迁移到 CommonDocumentUtils
    // - replaceInDocumentBody() -> CommonDocumentUtils.replaceInDocumentBody()
    // - processParagraphsAndTables() -> CommonDocumentUtils.processParagraphsAndTables()
    // - replaceParagraphText() -> CommonDocumentUtils.replaceParagraphText()
    
    public static byte[] generateDynamicInfoDocument(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        try (InputStream templateStream = CommonDocumentUtils.getTemplateInputStream(templatePath)) {
            XWPFDocument document = new XWPFDocument(templateStream);

            // 调试：分析模板文档
            CommonDocumentUtils.analyzeDocument(document, "模板加载后");

            // 1. 构建占位符参数
            Map<String, String> params = buildPlaceholderParams(data);

            // 2. 替换文档中的占位符（使用通用方法）
            CommonDocumentUtils.replaceInDocumentBody(document, params);
            CommonDocumentUtils.processParagraphsAndTables(document, params);

            // 3. 填充表格数据（仅动态信息）
            fillDynamicTableData(document, data);

            // 4. 检查处理后的文档
            CommonDocumentUtils.analyzeDocument(document, "处理完成后");

            // 5. 转换为字节数组（使用通用方法）
            return CommonDocumentUtils.documentToByteArray(document);
        }
    }
}
```

### 2. DynamicInfoPdfUtils 迁移示例

#### 迁移前：
```java
public class DynamicInfoPdfUtils {
    public static byte[] generateDynamicInfoPdf(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        try {
            byte[] wordBytes = DynamicInfoWordUtils.generateDynamicInfoDocument(templatePath, data, sealImagePath);
            
            ByteArrayInputStream wordInputStream = new ByteArrayInputStream(wordBytes);
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            
            boolean success = PdfConverUtils.wordToPdfByAspose(wordInputStream, pdfOutputStream);
            if (!success) {
                throw new Exception("Word转PDF失败");
            }
            
            return pdfOutputStream.toByteArray();
        }
    }
    
    public static byte[] generateDynamicInfoPdfBatch(...) {
        // 重复的ZIP压缩包创建逻辑...
    }
}
```

#### 迁移后：
```java
public class DynamicInfoPdfUtils {
    public static byte[] generateDynamicInfoPdf(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        try {
            byte[] wordBytes = DynamicInfoWordUtils.generateDynamicInfoDocument(templatePath, data, sealImagePath);
            
            // 使用通用方法进行Word转PDF
            return CommonDocumentUtils.wordToPdf(wordBytes);
        }
    }
    
    public static byte[] generateDynamicInfoPdfBatch(String templatePath, List<WordMeteorologicalVo> dataList, 
                                                     String sealImagePath, String fileNamePrefix) throws Exception {
        // 使用通用方法创建文档条目列表
        List<CommonDocumentUtils.DocumentEntry> documents = new ArrayList<>();
        
        for (int i = 0; i < dataList.size(); i++) {
            WordMeteorologicalVo data = dataList.get(i);
            try {
                byte[] pdfBytes = generateDynamicInfoPdf(templatePath, data, sealImagePath);
                String fileName = buildPdfFileName(data, fileNamePrefix, i + 1);
                documents.add(new CommonDocumentUtils.DocumentEntry(fileName, pdfBytes));
            } catch (Exception e) {
                log.error("生成第{}个PDF文档失败: {}", i + 1, e.getMessage());
            }
        }
        
        // 使用通用方法创建ZIP压缩包
        return CommonDocumentUtils.createZipArchive(documents, fileNamePrefix + "_批量PDF");
    }
}
```

## 其他工具类迁移指南

### WeatherInfoWordUtils 迁移

```java
// 原来的方法调用
try (InputStream templateStream = getTemplateInputStream(templatePath)) {
    // 替换为
try (InputStream templateStream = CommonDocumentUtils.getTemplateInputStream(templatePath)) {

// 原来的占位符替换
replaceInDocumentBody(document, params);
processParagraphsAndTables(document, params);
// 替换为
CommonDocumentUtils.replaceInDocumentBody(document, params);
CommonDocumentUtils.processParagraphsAndTables(document, params);

// 原来的文档转换
ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
document.write(outputStream);
return outputStream.toByteArray();
// 替换为
return CommonDocumentUtils.documentToByteArray(document);
```

### WeatherAndDynamicWordUtils 迁移

```java
// 如果使用了高级段落替换（保持格式）
replaceInParagraph(paragraph, params, true);
// 替换为
CommonDocumentUtils.replaceInParagraph(paragraph, params, true);
```

### PDF工具类通用迁移模式

```java
// 原来的Word转PDF
ByteArrayInputStream wordInputStream = new ByteArrayInputStream(wordBytes);
ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
boolean success = PdfConverUtils.wordToPdfByAspose(wordInputStream, pdfOutputStream);
if (!success) {
    throw new Exception("Word转PDF失败");
}
return pdfOutputStream.toByteArray();

// 替换为
return CommonDocumentUtils.wordToPdf(wordBytes);
```

### 批量处理迁移模式

```java
// 原来的ZIP创建逻辑
try (ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
     ZipOutputStream zip = new ZipOutputStream(zipOutputStream)) {
    
    for (int i = 0; i < dataList.size(); i++) {
        // 生成文档...
        ZipEntry zipEntry = new ZipEntry(fileName);
        zip.putNextEntry(zipEntry);
        zip.write(docBytes);
        zip.closeEntry();
    }
    
    zip.finish();
    return zipOutputStream.toByteArray();
}

// 替换为
List<CommonDocumentUtils.DocumentEntry> documents = new ArrayList<>();
for (int i = 0; i < dataList.size(); i++) {
    // 生成文档...
    documents.add(new CommonDocumentUtils.DocumentEntry(fileName, docBytes));
}
return CommonDocumentUtils.createZipArchive(documents, archiveName);
```

## 迁移步骤

### 第一步：添加导入
```java
import com.ruoyi.system.util.word.CommonDocumentUtils;
```

### 第二步：替换方法调用
按照上述示例，逐个替换方法调用。

### 第三步：删除重复方法
删除已经被通用方法替代的私有方法，并添加注释说明。

### 第四步：测试验证
运行测试确保功能正常。

## 迁移收益

### 代码减少统计
- **DynamicInfoWordUtils**: 减少约60行代码
- **DynamicInfoPdfUtils**: 减少约40行代码
- **预计总减少**: 约400-500行重复代码

### 功能增强
1. **统一的错误处理**
2. **详细的调试日志**
3. **图片保护机制**
4. **文档结构分析**
5. **占位符检查功能**

### 维护性提升
1. **集中的逻辑管理**
2. **一致的行为表现**
3. **易于功能扩展**
4. **统一的测试覆盖**

## 注意事项

1. **保持向后兼容**: 迁移过程中保持公共接口不变
2. **逐步迁移**: 一次迁移一个工具类，确保稳定性
3. **充分测试**: 每次迁移后都要进行完整测试
4. **文档更新**: 及时更新相关文档和注释

## 测试验证

使用 `CommonDocumentUtilsTest` 类验证通用方法的功能：

```bash
# 运行测试
mvn test -Dtest=CommonDocumentUtilsTest

# 运行特定测试方法
mvn test -Dtest=CommonDocumentUtilsTest#testCompleteDocumentProcessing
```

## 后续计划

1. **完成所有8个工具类的迁移**
2. **添加更多通用方法**（如图片处理、表格操作等）
3. **性能优化**（如模板缓存、批量处理优化）
4. **监控和告警**（添加文档生成的监控指标）
