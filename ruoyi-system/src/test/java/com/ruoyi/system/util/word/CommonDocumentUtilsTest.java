package com.ruoyi.system.util.word;

import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;

import static org.hibernate.validator.internal.util.Contracts.assertNotNull;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 通用文档工具类测试
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
public class CommonDocumentUtilsTest {

    /**
     * 测试模板加载功能
     */
    @Test
    public void testGetTemplateInputStream() {
        try {
            // 测试加载存在的模板文件
            String templatePath = "word/动态信息模板.docx";
            InputStream stream = CommonDocumentUtils.getTemplateInputStream(templatePath);
            assertNotNull(stream, "模板文件应该能够成功加载");
            
            // 验证流可以读取
            assertTrue(stream.available() > 0, "模板文件流应该包含数据");
            stream.close();
            
            log.info("✅ 模板加载测试通过");
            
        } catch (Exception e) {
            log.error("❌ 模板加载测试失败", e);
            fail("模板加载测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试占位符替换功能
     */
    @Test
    public void testPlaceholderReplacement() {
        try {
            // 创建测试文档
            XWPFDocument document = new XWPFDocument();
            document.createParagraph().createRun().setText("机型: ${aircraftType}, 注册号: ${registrationNumber}");
            
            // 创建占位符参数
            Map<String, String> params = new HashMap<>();
            params.put("${aircraftType}", "A320");
            params.put("${registrationNumber}", "B-1234");
            
            // 执行替换
            CommonDocumentUtils.replaceInDocumentBody(document, params);
            
            // 验证替换结果
            String text = document.getParagraphs().get(0).getText();
            assertTrue(text.contains("A320"), "应该包含替换后的机型");
            assertTrue(text.contains("B-1234"), "应该包含替换后的注册号");
            assertFalse(text.contains("${aircraftType}"), "不应该包含原始占位符");
            
            log.info("✅ 占位符替换测试通过: {}", text);
            
        } catch (Exception e) {
            log.error("❌ 占位符替换测试失败", e);
            fail("占位符替换测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试文档分析功能
     */
    @Test
    public void testDocumentAnalysis() {
        try {
            // 创建测试文档
            XWPFDocument document = new XWPFDocument();
            document.createParagraph().createRun().setText("测试段落");
            document.createTable(2, 2);
            
            // 执行分析（主要测试不会抛出异常）
            CommonDocumentUtils.analyzeDocument(document, "测试阶段");
            
            log.info("✅ 文档分析测试通过");
            
        } catch (Exception e) {
            log.error("❌ 文档分析测试失败", e);
            fail("文档分析测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试占位符检查功能
     */
    @Test
    public void testContainsPlaceholder() {
        try {
            // 创建测试文档
            XWPFDocument document = new XWPFDocument();
            document.createParagraph().createRun().setText("机型: ${aircraftType}");
            
            // 测试存在的占位符
            assertTrue(CommonDocumentUtils.containsPlaceholder(document, "${aircraftType}"), 
                      "应该检测到存在的占位符");
            
            // 测试不存在的占位符
            assertFalse(CommonDocumentUtils.containsPlaceholder(document, "${notExist}"), 
                       "不应该检测到不存在的占位符");
            
            log.info("✅ 占位符检查测试通过");
            
        } catch (Exception e) {
            log.error("❌ 占位符检查测试失败", e);
            fail("占位符检查测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试文档转换功能
     */
    @Test
    public void testDocumentToByteArray() {
        try {
            // 创建测试文档
            XWPFDocument document = new XWPFDocument();
            document.createParagraph().createRun().setText("测试内容");
            
            // 转换为字节数组
            byte[] docBytes = CommonDocumentUtils.documentToByteArray(document);
            
            assertNotNull(docBytes, "字节数组不应该为空");
            assertTrue(docBytes.length > 0, "字节数组应该包含数据");
            
            // 验证可以重新读取
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(docBytes)) {
                XWPFDocument newDocument = new XWPFDocument(inputStream);
                assertEquals("测试内容", newDocument.getParagraphs().get(0).getText(), 
                           "重新读取的文档内容应该一致");
            }
            
            log.info("✅ 文档转换测试通过，文档大小: {} 字节", docBytes.length);
            
        } catch (Exception e) {
            log.error("❌ 文档转换测试失败", e);
            fail("文档转换测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试文件名生成功能
     */
    @Test
    public void testFileNameGeneration() {
        try {
            // 测试基本文件名生成
            String fileName1 = CommonDocumentUtils.generateFileName("测试文档", "docx");
            assertNotNull(fileName1, "文件名不应该为空");
            assertTrue(fileName1.contains("测试文档"), "文件名应该包含前缀");
            assertTrue(fileName1.endsWith(".docx"), "文件名应该包含正确的扩展名");
            
            // 测试带序号的文件名生成
            String fileName2 = CommonDocumentUtils.generateFileNameWithIndex("测试文档", "pdf", 1);
            assertNotNull(fileName2, "带序号的文件名不应该为空");
            assertTrue(fileName2.contains("001"), "文件名应该包含格式化的序号");
            
            log.info("✅ 文件名生成测试通过");
            log.info("  基本文件名: {}", fileName1);
            log.info("  带序号文件名: {}", fileName2);
            
        } catch (Exception e) {
            log.error("❌ 文件名生成测试失败", e);
            fail("文件名生成测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试ZIP压缩包创建功能
     */
    @Test
    public void testCreateZipArchive() {
        try {
            // 创建测试文档条目
            List<CommonDocumentUtils.DocumentEntry> documents = new ArrayList<>();
            
            // 添加第一个文档
            XWPFDocument doc1 = new XWPFDocument();
            doc1.createParagraph().createRun().setText("文档1内容");
            byte[] doc1Bytes = CommonDocumentUtils.documentToByteArray(doc1);
            documents.add(new CommonDocumentUtils.DocumentEntry("文档1.docx", doc1Bytes));
            
            // 添加第二个文档
            XWPFDocument doc2 = new XWPFDocument();
            doc2.createParagraph().createRun().setText("文档2内容");
            byte[] doc2Bytes = CommonDocumentUtils.documentToByteArray(doc2);
            documents.add(new CommonDocumentUtils.DocumentEntry("文档2.docx", doc2Bytes));
            
            // 创建ZIP压缩包
            byte[] zipBytes = CommonDocumentUtils.createZipArchive(documents, "测试压缩包");
            
            assertNotNull(zipBytes, "ZIP字节数组不应该为空");
            assertTrue(zipBytes.length > 0, "ZIP字节数组应该包含数据");
            
            log.info("✅ ZIP压缩包创建测试通过，压缩包大小: {} 字节", zipBytes.length);
            
        } catch (Exception e) {
            log.error("❌ ZIP压缩包创建测试失败", e);
            fail("ZIP压缩包创建测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试Word转PDF功能（需要Aspose许可证）
     */
    @Test
    public void testWordToPdf() {
        try {
            // 创建测试Word文档
            XWPFDocument document = new XWPFDocument();
            document.createParagraph().createRun().setText("测试PDF转换");
            byte[] wordBytes = CommonDocumentUtils.documentToByteArray(document);
            
            // 转换为PDF
            byte[] pdfBytes = CommonDocumentUtils.wordToPdf(wordBytes);
            
            assertNotNull(pdfBytes, "PDF字节数组不应该为空");
            assertTrue(pdfBytes.length > 0, "PDF字节数组应该包含数据");
            
            log.info("✅ Word转PDF测试通过，PDF大小: {} 字节", pdfBytes.length);
            
        } catch (Exception e) {
            log.warn("⚠️ Word转PDF测试跳过（可能缺少Aspose许可证）: {}", e.getMessage());
            // 这个测试可能因为缺少Aspose许可证而失败，所以只记录警告
        }
    }

    /**
     * 集成测试：测试完整的文档处理流程
     */
    @Test
    public void testCompleteDocumentProcessing() {
        try {
            log.info("开始集成测试：完整文档处理流程");
            
            // 1. 创建测试数据
            WordMeteorologicalVo testData = new WordMeteorologicalVo();
            testData.setAircraftType("A320");
            testData.setRegistrationNumber("B-1234");
            testData.setFlightDate("2024-01-01");
            testData.setFuel("5000");
            
            // 2. 测试动态信息Word文档生成
            String templatePath = "word/动态信息模板.docx";
            byte[] wordBytes = DynamicInfoWordUtils.generateDynamicInfoDocument(templatePath, testData, null);
            
            assertNotNull(wordBytes, "Word文档字节数组不应该为空");
            assertTrue(wordBytes.length > 0, "Word文档应该包含数据");
            
            // 3. 测试PDF转换
            try {
                byte[] pdfBytes = CommonDocumentUtils.wordToPdf(wordBytes);
                assertNotNull(pdfBytes, "PDF文档字节数组不应该为空");
                log.info("✅ PDF转换成功，大小: {} 字节", pdfBytes.length);
            } catch (Exception e) {
                log.warn("⚠️ PDF转换跳过: {}", e.getMessage());
            }
            
            log.info("✅ 集成测试通过");
            
        } catch (Exception e) {
            log.error("❌ 集成测试失败", e);
            fail("集成测试失败: " + e.getMessage());
        }
    }
}
