package com.ruoyi.system.service.oc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.oc.FlightTaskBook;
import com.ruoyi.system.domain.oc.FlightTaskInfo;
import com.ruoyi.system.domain.oc.dto.FlightDynamicDto;
import com.ruoyi.system.domain.oc.dto.FlightDynamicInfoDto;
import com.ruoyi.system.domain.oc.entity.FlightDynamicInfo;
import com.ruoyi.system.domain.oc.entity.FlightWeatherInfo;
import com.ruoyi.system.domain.oc.vo.FlightDynamicInfoVo;
import com.ruoyi.system.domain.oc.vo.FlightDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.mapper.oc.FlightDynamicInfoMapper;
import com.ruoyi.system.mapper.oc.FlightTaskBookMapper;
import com.ruoyi.system.mapper.oc.FlightTaskInfoMapper;
import com.ruoyi.system.service.oc.IFlightDynamicInfoService;
import com.ruoyi.system.util.word.DynamicInfoWordUtils;
import com.ruoyi.system.util.word.DynamicInfoPdfUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 动态信息服务实现
 * <AUTHOR>
 */
@Service
@Slf4j
public class FlightDynamicInfoServiceImpl extends ServiceImpl<FlightDynamicInfoMapper, FlightDynamicInfo> implements IFlightDynamicInfoService {

    @Resource
    private FlightDynamicInfoMapper flightDynamicInfoMapper;
    @Resource
    private FlightTaskBookMapper flightTaskBookMapper;
    @Resource
    private FlightTaskInfoMapper flightTaskInfoMapper;
    @Value("${template.dynamicTemplatePath}")
    private String dynamicTemplate;
    @Value("${sealImagePath}")
    private String sealImagePath;
    @Resource
    private IFlightDynamicInfoService iFlightDynamicInfoService;

    @Override
    public FlightDynamicVo selectFlightDynamicInfoById(Integer flightTaskBookId, String companyCode) {
        log.info("查询动态信息，任务书ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
        // 查询动态信息列表
        FlightDynamicVo flightDynamicVo = flightDynamicInfoMapper.selectFlightDynamicVoByTaskBookNumber(flightTaskBookId, companyCode);
        if (flightDynamicVo != null) {
            // 计算统计信息
            calculateTotalInfo(flightDynamicVo);
        }
        return flightDynamicVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveFlightDynamicInfo(String companyCode, FlightDynamicDto flightDynamicDto) {
        FlightTaskBook dbTaskBook = flightTaskBookMapper.selectByPrimaryKey(flightDynamicDto.getTaskBookId());
        if (dbTaskBook != null && dbTaskBook.getCompanyCode().equals(companyCode)) {
            String taskBookNumber = dbTaskBook.getTaskBookNumber();
            List<FlightDynamicInfoDto> dynamicInfoDtos = flightDynamicDto.getFlightDynamicInfoDtos();
            if (CollUtil.isNotEmpty(dynamicInfoDtos)) {
                //先删除旧数据
                iFlightDynamicInfoService.lambdaUpdate().eq(FlightDynamicInfo::getTaskBookNumber, taskBookNumber).remove();
                //封装新增的数据
                saveDynamicInfo(companyCode, dynamicInfoDtos, taskBookNumber);
            }
            return 1;
        }
        return 0;
    }

    /**
     * 保存动态信息
     */
    private void saveDynamicInfo(String companyCode, List<FlightDynamicInfoDto> flightDynamicInfoDtos, String taskBookNumber) {
        List<FlightDynamicInfo> flightDynamicInfos = new ArrayList<>();
        List<FlightTaskInfo> flightTaskInfos = flightTaskInfoMapper.selectByTaskBookNumber(taskBookNumber, companyCode);

        if (CollUtil.isNotEmpty(flightTaskInfos)) {
            // 筛选批次匹配的数据
            List<FlightDynamicInfoDto> matchingDynamicDtos = new ArrayList<>();
            for (FlightDynamicInfoDto dynamicDto : flightDynamicInfoDtos) {
                for (FlightTaskInfo taskInfo : flightTaskInfos) {
                    if (StrUtil.equals(dynamicDto.getBatch(), taskInfo.getBatch())) {
                        matchingDynamicDtos.add(dynamicDto);
                        break;
                    }
                }
            }
            flightDynamicInfoDtos = matchingDynamicDtos;
        }

        flightDynamicInfoDtos.forEach(dto -> {
            FlightDynamicInfo flightDynamicInfo = new FlightDynamicInfo();
            BeanUtils.copyProperties(dto, flightDynamicInfo);
            flightDynamicInfo.setTaskBookNumber(taskBookNumber);
            flightDynamicInfo.setCompanyCode(companyCode);
            flightDynamicInfos.add(flightDynamicInfo);
        });

        iFlightDynamicInfoService.saveBatch(flightDynamicInfos);
    }

    @Override
    public void exportDynamicInfoWord(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {
        log.info("开始导出动态信息Word文档，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
        try {
            // 查询动态信息数据
            FlightDynamicVo flightDynamicVo = this.selectFlightDynamicInfoById(flightTaskBookId, companyCode);
            if (flightDynamicVo == null) {
                log.error("未找到对应的动态数据，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
                return;
            }

            // 转换为Word导出格式
            WordMeteorologicalVo wordMeteorologicalVo = convertToDynamicWordVo(flightDynamicVo);

            // 生成Word文档
            byte[] docBytes = generateDynamicWordDocument(wordMeteorologicalVo);

            // 设置响应头
            String fileName = DynamicInfoWordUtils.generateFileName("动态信息记录表", "docx");
            DynamicInfoWordUtils.setWordResponseHeaders(response, fileName);

            // 输出文件到前端
            DynamicInfoWordUtils.streamWordDocumentToResponse(response, docBytes);

            log.info("动态信息Word文档导出成功，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("导出动态信息Word文档失败，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode, e);
        }
    }

    @Override
    public void exportDynamicInfoPdf(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {
        log.info("开始导出动态信息PDF文档，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
        try {
            // 查询动态信息数据
            FlightDynamicVo flightDynamicVo = this.selectFlightDynamicInfoById(flightTaskBookId, companyCode);
            if (flightDynamicVo == null) {
                log.error("未找到对应的动态数据，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
                return;
            }

            // 转换为Word导出格式
            WordMeteorologicalVo wordMeteorologicalVo = convertToDynamicWordVo(flightDynamicVo);

            // 生成PDF文档
            byte[] pdfBytes = DynamicInfoPdfUtils.generateDynamicInfoPdf(dynamicTemplate, wordMeteorologicalVo, sealImagePath);

            // 设置响应头
            String fileName = DynamicInfoPdfUtils.generateFileName("动态信息记录表", "pdf");
            DynamicInfoPdfUtils.setPdfResponseHeaders(response, fileName);

            // 输出文件到前端
            DynamicInfoPdfUtils.streamPdfDocumentToResponse(response, pdfBytes);

            log.info("动态信息PDF文档导出成功，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("导出动态信息PDF文档失败，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode, e);
        }
    }

    @Override
    public void exportDynamicInfoPdfBatch(HttpServletResponse response, List<Integer> flightTaskBookIds, String companyCode) {
        log.info("开始批量导出动态信息PDF文档，任务数量: {}, 公司代码: {}", flightTaskBookIds.size(), companyCode);
        try {
            // 准备数据列表
            List<WordMeteorologicalVo> dataList = new ArrayList<>();

            for (Integer flightTaskBookId : flightTaskBookIds) {
                try {
                    FlightDynamicVo flightDynamicVo = this.selectFlightDynamicInfoById(flightTaskBookId, companyCode);
                    if (flightDynamicVo != null) {
                        WordMeteorologicalVo wordMeteorologicalVo = convertToDynamicWordVo(flightDynamicVo);
                        dataList.add(wordMeteorologicalVo);
                    } else {
                        log.warn("任务书ID: {} 未找到对应的动态数据", flightTaskBookId);
                    }
                } catch (Exception e) {
                    log.error("处理任务书ID: {} 时发生错误", flightTaskBookId, e);
                }
            }

            if (dataList.isEmpty()) {
                log.error("没有找到任何有效的动态数据");
                return;
            }

            // 生成批量PDF压缩包
            byte[] zipBytes = DynamicInfoPdfUtils.generateDynamicInfoPdfBatch(dynamicTemplate, dataList, sealImagePath, "动态信息记录表");

            // 设置响应头
            String fileName = DynamicInfoPdfUtils.generateFileName("动态信息记录表批量导出", "zip");
            DynamicInfoPdfUtils.setZipResponseHeaders(response, fileName);

            // 输出文件到前端
            DynamicInfoPdfUtils.streamZipDocumentToResponse(response, zipBytes);

            log.info("动态信息PDF批量导出成功，文件名: {}, 处理数量: {}", fileName, dataList.size());
        } catch (Exception e) {
            log.error("批量导出动态信息PDF文档失败，公司代码: {}", companyCode, e);
        }
    }

    @Override
    public byte[] generateDynamicWordDocument(WordMeteorologicalVo wordMeteorologicalVo) {
        try {
            return DynamicInfoWordUtils.generateDynamicInfoDocument(dynamicTemplate, wordMeteorologicalVo, sealImagePath);
        } catch (Exception e) {
            log.error("生成动态信息Word文档失败", e);
            throw new RuntimeException("生成Word文档失败: " + e.getMessage(), e);
        }
    }


    /**
     * 转换动态信息为Word导出格式（仅动态信息，不包含气象信息）
     */
    private WordMeteorologicalVo convertToDynamicWordVo(FlightDynamicVo flightDynamicVo) {
        WordMeteorologicalVo wordMeteorologicalVo = new WordMeteorologicalVo();
        BeanUtils.copyProperties(flightDynamicVo, wordMeteorologicalVo);

        // 转换动态信息列表
        List<FlightDynamicInfoVo> flightDynamicInfoVos = flightDynamicVo.getFlightDynamicInfoVos();
        List<WordFlightWeatherDynamicVo> dynamicInfoList = new ArrayList<>();

        if (flightDynamicInfoVos != null) {
            flightDynamicInfoVos = flightDynamicInfoVos.stream().filter(vo -> vo.getId() != null).collect(Collectors.toList());
            flightDynamicInfoVos.forEach(flightDynamicInfo -> {
                WordFlightWeatherDynamicVo wordFlightWeatherDynamicVo = new WordFlightWeatherDynamicVo();
                BeanUtils.copyProperties(flightDynamicInfo, wordFlightWeatherDynamicVo);
                dynamicInfoList.add(wordFlightWeatherDynamicVo);
            });
        }

        wordMeteorologicalVo.setDynamicInfoList(dynamicInfoList);

        // 动态信息导出不包含气象信息
        wordMeteorologicalVo.setDepartureWeatherInfoList(new ArrayList<>());
        wordMeteorologicalVo.setArrivalWeatherInfoList(new ArrayList<>());

        return wordMeteorologicalVo;
    }

    /**
     * 计算统计信息
     */
    private void calculateTotalInfo(FlightDynamicVo flightDynamicVo) {
        if (flightDynamicVo.getFlightDynamicInfoVos() == null || flightDynamicVo.getFlightDynamicInfoVos().isEmpty()) {
            return;
        }
        List<FlightDynamicInfoVo> flightDynamicInfoVos = flightDynamicVo.getFlightDynamicInfoVos();
        flightDynamicInfoVos = flightDynamicInfoVos.stream().filter(dynamicInfoVo -> dynamicInfoVo.getId() != null).collect(Collectors.toList());
        int totalGroundTimeMin = 0;
        int totalAirTimeMin = 0;
        int totalTimeMin = 0;
        int totalSortieCount = 0;

        for (FlightDynamicInfoVo infoVo : flightDynamicInfoVos) {
            if (infoVo.getGroundTimeMin() != null) {
                totalGroundTimeMin += infoVo.getGroundTimeMin();
            }
            if (infoVo.getAirTimeMin() != null) {
                totalAirTimeMin += infoVo.getAirTimeMin();
            }
            if (infoVo.getTotalTimeMin() != null) {
                totalTimeMin += infoVo.getTotalTimeMin();
            }
            if (infoVo.getSortieCount() != null) {
                totalSortieCount += infoVo.getSortieCount();
            }
        }

        flightDynamicVo.setGroundTimeMinTotal(String.valueOf(totalGroundTimeMin));
        flightDynamicVo.setAirTimeMinTotal(String.valueOf(totalAirTimeMin));
        flightDynamicVo.setTotalTimeMinTotal(String.valueOf(totalTimeMin));
        flightDynamicVo.setSortieCountTotal(String.valueOf(totalSortieCount));
    }
}
