package com.ruoyi.system.util.word;

import com.ruoyi.common.utils.PdfConverUtils;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 动态信息PDF文档处理工具类
 */
@Slf4j
public class DynamicInfoPdfUtils {

    /**
     * 生成动态信息PDF文档
     *
     * @param templatePath  模板文件路径
     * @param data          动态信息数据
     * @param sealImagePath 公章图片路径
     * @return PDF文档字节数组
     * @throws Exception 处理异常
     */
    public static byte[] generateDynamicInfoPdf(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        log.info("开始生成动态信息PDF文档，模板路径: {}", templatePath);
        
        try {
            // 1. 先生成Word文档
            byte[] wordBytes = DynamicInfoWordUtils.generateDynamicInfoDocument(templatePath, data, sealImagePath);

            // 2. 将Word转换为PDF（使用通用方法）
            return CommonDocumentUtils.wordToPdf(wordBytes);
            
        } catch (Exception e) {
            log.error("生成动态信息PDF文档失败", e);
            throw new Exception("生成PDF文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量生成动态信息PDF文档并压缩
     *
     * @param templatePath    模板文件路径
     * @param dataList        动态信息数据列表
     * @param sealImagePath   公章图片路径
     * @param fileNamePrefix  文件名前缀
     * @return ZIP压缩包字节数组
     * @throws Exception 处理异常
     */
    public static byte[] generateDynamicInfoPdfBatch(String templatePath, List<WordMeteorologicalVo> dataList, 
                                                     String sealImagePath, String fileNamePrefix) throws Exception {
        log.info("开始批量生成动态信息PDF文档，数量: {}", dataList.size());

        // 使用通用方法创建文档条目列表
        List<CommonDocumentUtils.DocumentEntry> documents = new ArrayList<>();

        for (int i = 0; i < dataList.size(); i++) {
            WordMeteorologicalVo data = dataList.get(i);

            try {
                // 生成单个PDF
                byte[] pdfBytes = generateDynamicInfoPdf(templatePath, data, sealImagePath);

                // 构建文件名
                String fileName = buildPdfFileName(data, fileNamePrefix, i + 1);

                // 添加到文档条目列表
                documents.add(new CommonDocumentUtils.DocumentEntry(fileName, pdfBytes));

                log.info("已生成PDF文档: {}", fileName);

            } catch (Exception e) {
                log.error("生成第{}个PDF文档失败: {}", i + 1, e.getMessage());
                // 继续处理其他文档，不中断整个批量操作
            }
        }

        // 使用通用方法创建ZIP压缩包
        return CommonDocumentUtils.createZipArchive(documents, fileNamePrefix + "_批量PDF");
            
        } catch (Exception e) {
            log.error("批量生成动态信息PDF文档失败", e);
            throw new Exception("批量生成PDF文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建PDF文件名
     */
    private static String buildPdfFileName(WordMeteorologicalVo data, String prefix, int index) {
        StringBuilder fileName = new StringBuilder();
        
        fileName.append(prefix);
        
        // 添加机型和注册号信息
        if (data.getAircraftType() != null && !data.getAircraftType().isEmpty()) {
            fileName.append("_").append(data.getAircraftType());
        }
        if (data.getRegistrationNumber() != null && !data.getRegistrationNumber().isEmpty()) {
            fileName.append("_").append(data.getRegistrationNumber());
        }
        
        // 添加飞行日期
        if (data.getFlightDate() != null && !data.getFlightDate().isEmpty()) {
            fileName.append("_").append(data.getFlightDate());
        }
        
        // 添加序号
        fileName.append("_").append(String.format("%03d", index));
        
        // 添加时间戳
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));
        fileName.append("_").append(timestamp);
        
        fileName.append(".pdf");
        
        return fileName.toString();
    }

    // 文件名生成和响应头设置方法已迁移到 CommonDocumentUtils
    // - generateFileName() -> CommonDocumentUtils.generateFileName()
    // - setPdfResponseHeaders() -> CommonDocumentUtils.streamPdfDocumentToResponse()

    // 文档输出相关方法已迁移到 CommonDocumentUtils
    // - setZipResponseHeaders() + streamZipDocumentToResponse() -> CommonDocumentUtils.streamZipToResponse()
    // - streamPdfDocumentToResponse() -> CommonDocumentUtils.streamPdfDocumentToResponse()
}
