package com.ruoyi.system.util.word;

import com.ruoyi.common.utils.PdfConverUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 通用文档处理工具类
 * 提取所有Word/PDF工具类的通用方法
 * 
 * <AUTHOR>
 */
@Slf4j
public class CommonDocumentUtils {

    // ==================== 模板加载相关 ====================

    /**
     * 获取模板输入流
     * 通用方法：从类路径或文件系统加载模板文件
     */
    public static InputStream getTemplateInputStream(String templatePath) throws Exception {
        try {
            log.info("尝试加载模板文件: {}", templatePath);
            
            // 尝试从类路径加载
            InputStream stream = CommonDocumentUtils.class.getClassLoader().getResourceAsStream(templatePath);
            if (stream != null) {
                log.info("✅ 从类路径成功加载模板文件: {}", templatePath);
                return stream;
            } else {
                log.warn("❌ 从类路径加载模板文件失败: {}", templatePath);
            }

            // 尝试从文件系统加载
            if (Files.exists(Paths.get(templatePath))) {
                log.info("✅ 从文件系统成功加载模板文件: {}", templatePath);
                return Files.newInputStream(Paths.get(templatePath));
            } else {
                log.warn("❌ 文件系统中不存在模板文件: {}", templatePath);
            }

            throw new FileNotFoundException("模板文件未找到: " + templatePath);
        } catch (Exception e) {
            log.error("❌ 加载模板文件失败: {}", templatePath, e);
            throw new Exception("加载模板文件失败: " + e.getMessage(), e);
        }
    }

    // ==================== 占位符替换相关 ====================

    /**
     * 替换文档正文中的占位符
     * 通用方法：处理文档主体段落的占位符替换
     */
    public static void replaceInDocumentBody(XWPFDocument document, Map<String, String> params) {
        log.info("开始替换文档正文中的占位符");
        
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceParagraphText(paragraph, params);
        }
        
        log.info("文档正文占位符替换完成");
    }

    /**
     * 处理段落和表格中的占位符
     * 通用方法：处理表格中的占位符替换
     */
    public static void processParagraphsAndTables(XWPFDocument document, Map<String, String> params) {
        log.info("开始处理段落和表格中的占位符");
        
        // 处理表格中的占位符
        for (XWPFTable table : document.getTables()) {
            processTable(table, params);
        }
        
        log.info("段落和表格占位符处理完成");
    }

    /**
     * 处理表格中的占位符（递归处理嵌套表格）
     * 通用方法：递归处理表格及其嵌套表格的占位符
     */
    public static void processTable(XWPFTable table, Map<String, String> params) {
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    replaceParagraphText(paragraph, params);
                }
                // 递归处理嵌套表格
                for (XWPFTable nestedTable : cell.getTables()) {
                    processTable(nestedTable, params);
                }
            }
        }
    }

    /**
     * 替换段落文本
     * 通用方法：替换段落中的占位符文本
     */
    public static void replaceParagraphText(XWPFParagraph paragraph, Map<String, String> params) {
        String text = paragraph.getText();
        if (text != null) {
            String originalText = text;
            boolean hasPlaceholder = false;
            
            // 检查是否包含占位符
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (text.contains(entry.getKey())) {
                    text = text.replace(entry.getKey(), entry.getValue());
                    hasPlaceholder = true;
                }
            }
            
            // 只有当包含占位符且文本发生变化时才重新设置段落内容
            if (hasPlaceholder && !text.equals(originalText)) {
                log.debug("替换段落占位符: '{}' -> '{}'", originalText, text);
                
                // 检查段落是否包含图片（保护图片不被删除）
                boolean hasImages = false;
                for (XWPFRun run : paragraph.getRuns()) {
                    if (run.getEmbeddedPictures().size() > 0) {
                        hasImages = true;
                        log.warn("⚠️ 段落包含图片，跳过文本替换以保护图片");
                        break;
                    }
                }
                
                // 如果段落包含图片，则不进行替换操作
                if (!hasImages) {
                    // 清除原有内容
                    for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                        paragraph.removeRun(i);
                    }
                    // 添加新内容
                    XWPFRun run = paragraph.createRun();
                    run.setText(text);
                } else {
                    log.info("保护包含图片的段落，不进行文本替换");
                }
            }
        }
    }

    /**
     * 高级段落替换（支持保持格式）
     * 通用方法：基于Run级别的占位符替换，保持原有格式
     */
    public static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> params, boolean preserveFormat) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) return;

        for (XWPFRun run : runs) {
            String text = run.getText(0);
            if (text == null || text.isEmpty()) continue;

            boolean replaced = false;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (text.contains(entry.getKey())) {
                    text = text.replace(entry.getKey(), entry.getValue());
                    replaced = true;
                }
            }

            if (replaced) {
                run.setText(text, 0);
                if (preserveFormat) {
                    // 保持原有格式，或设置默认格式
                    if (run.getFontFamily() == null) {
                        run.setFontFamily("宋体");
                    }
                    if (run.getFontSize() == -1) {
                        run.setFontSize(9);
                    }
                }
            }
        }
    }

    // ==================== Word转PDF相关 ====================

    /**
     * Word字节数组转PDF字节数组
     * 通用方法：将Word文档字节数组转换为PDF字节数组
     */
    public static byte[] wordToPdf(byte[] wordBytes) throws Exception {
        log.info("开始Word转PDF，Word文档大小: {} 字节", wordBytes.length);
        
        try {
            ByteArrayInputStream wordInputStream = new ByteArrayInputStream(wordBytes);
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            
            boolean success = PdfConverUtils.wordToPdfByAspose(wordInputStream, pdfOutputStream);
            if (!success) {
                throw new Exception("Word转PDF失败");
            }
            
            byte[] pdfBytes = pdfOutputStream.toByteArray();
            log.info("Word转PDF完成，PDF文档大小: {} 字节", pdfBytes.length);
            return pdfBytes;
            
        } catch (Exception e) {
            log.error("Word转PDF失败", e);
            throw new Exception("Word转PDF失败: " + e.getMessage(), e);
        }
    }

    // ==================== 文档输出相关 ====================

    /**
     * 输出文档到HTTP响应流
     * 通用方法：将文档字节数组输出到HTTP响应
     */
    public static void streamDocumentToResponse(HttpServletResponse response, byte[] docBytes, 
                                              String fileName, String contentType) {
        try {
            // 设置响应头
            response.setContentType(contentType);
            response.setCharacterEncoding("UTF-8");
            
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
            
            // 输出文档
            try (OutputStream outputStream = response.getOutputStream()) {
                outputStream.write(docBytes);
                outputStream.flush();
                log.info("文档输出到响应流完成，文件名: {}, 大小: {} 字节", fileName, docBytes.length);
            }
            
        } catch (Exception e) {
            log.error("输出文档到响应流失败", e);
        }
    }

    /**
     * 输出Word文档到响应流
     * 通用方法：输出Word文档的便捷方法
     */
    public static void streamWordDocumentToResponse(HttpServletResponse response, byte[] docBytes, String fileName) {
        streamDocumentToResponse(response, docBytes, fileName, 
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
    }

    /**
     * 输出PDF文档到响应流
     * 通用方法：输出PDF文档的便捷方法
     */
    public static void streamPdfDocumentToResponse(HttpServletResponse response, byte[] docBytes, String fileName) {
        streamDocumentToResponse(response, docBytes, fileName, "application/pdf");
    }

    // ==================== 文件名生成相关 ====================

    /**
     * 生成带时间戳的文件名
     * 通用方法：生成唯一的文件名
     */
    public static String generateFileName(String prefix, String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return prefix + "_" + timestamp + "." + extension;
    }

    /**
     * 生成带序号的文件名
     * 通用方法：为批量文件生成带序号的文件名
     */
    public static String generateFileNameWithIndex(String prefix, String extension, int index) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return prefix + "_" + String.format("%03d", index) + "_" + timestamp + "." + extension;
    }

    // ==================== 文档转换相关 ====================

    /**
     * 将XWPFDocument转换为字节数组
     * 通用方法：将Word文档对象转换为字节数组
     */
    public static byte[] documentToByteArray(XWPFDocument document) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            document.write(outputStream);
            byte[] docBytes = outputStream.toByteArray();
            log.info("文档转换为字节数组完成，大小: {} 字节", docBytes.length);
            return docBytes;
        } catch (Exception e) {
            log.error("文档转换为字节数组失败", e);
            throw new Exception("文档转换失败: " + e.getMessage(), e);
        }
    }

    // ==================== 批量处理相关 ====================

    /**
     * 批量生成ZIP压缩包
     * 通用方法：将多个文档打包成ZIP文件
     */
    public static byte[] createZipArchive(List<DocumentEntry> documents, String archiveName) throws Exception {
        log.info("开始创建ZIP压缩包，文档数量: {}", documents.size());

        try (ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
             ZipOutputStream zip = new ZipOutputStream(zipOutputStream)) {

            for (int i = 0; i < documents.size(); i++) {
                DocumentEntry entry = documents.get(i);

                try {
                    // 添加到压缩包
                    ZipEntry zipEntry = new ZipEntry(entry.getFileName());
                    zip.putNextEntry(zipEntry);
                    zip.write(entry.getDocumentBytes());
                    zip.closeEntry();

                    log.info("已添加文件到压缩包: {}", entry.getFileName());

                } catch (Exception e) {
                    log.error("添加第{}个文档到压缩包失败: {}", i + 1, e.getMessage());
                    // 继续处理其他文档，不中断整个批量操作
                }
            }

            zip.finish();
            byte[] zipBytes = zipOutputStream.toByteArray();
            log.info("ZIP压缩包创建完成，大小: {} 字节", zipBytes.length);
            return zipBytes;

        } catch (Exception e) {
            log.error("创建ZIP压缩包失败", e);
            throw new Exception("创建ZIP压缩包失败: " + e.getMessage(), e);
        }
    }

    /**
     * 输出ZIP压缩包到响应流
     * 通用方法：输出ZIP文件的便捷方法
     */
    public static void streamZipToResponse(HttpServletResponse response, byte[] zipBytes, String fileName) {
        streamDocumentToResponse(response, zipBytes, fileName, "application/zip");
    }

    // ==================== 文档条目类 ====================

    /**
     * 文档条目类
     * 用于批量处理时存储文档信息
     */
    public static class DocumentEntry {
        private String fileName;
        private byte[] documentBytes;

        public DocumentEntry(String fileName, byte[] documentBytes) {
            this.fileName = fileName;
            this.documentBytes = documentBytes;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public byte[] getDocumentBytes() {
            return documentBytes;
        }

        public void setDocumentBytes(byte[] documentBytes) {
            this.documentBytes = documentBytes;
        }
    }

    // ==================== 调试和分析相关 ====================

    /**
     * 分析文档结构
     * 通用方法：分析Word文档的基本结构信息
     */
    public static void analyzeDocument(XWPFDocument document, String stage) {
        log.info("📊 文档分析 - {}:", stage);
        log.info("  - 图片数量: {}", document.getAllPictures().size());
        log.info("  - 段落数量: {}", document.getParagraphs().size());
        log.info("  - 表格数量: {}", document.getTables().size());

        if (document.getAllPictures().size() > 0) {
            log.info("✅ 文档包含图片");
            for (int i = 0; i < document.getAllPictures().size(); i++) {
                try {
                    XWPFPictureData pic = document.getAllPictures().get(i);
                    log.info("  - 图片{}: 类型={}, 大小={}字节", i+1, pic.getPictureType(), pic.getData().length);
                } catch (Exception e) {
                    log.warn("  - 图片{}信息获取失败: {}", i+1, e.getMessage());
                }
            }
        } else {
            log.warn("❌ 文档不包含图片");
        }
    }

    /**
     * 检查占位符是否存在
     * 通用方法：检查文档中是否包含指定的占位符
     */
    public static boolean containsPlaceholder(XWPFDocument document, String placeholder) {
        // 检查段落
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            if (paragraph.getText().contains(placeholder)) {
                return true;
            }
        }

        // 检查表格
        for (XWPFTable table : document.getTables()) {
            if (containsPlaceholderInTable(table, placeholder)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查表格中是否包含占位符
     * 通用方法：递归检查表格及嵌套表格中的占位符
     */
    private static boolean containsPlaceholderInTable(XWPFTable table, String placeholder) {
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    if (paragraph.getText().contains(placeholder)) {
                        return true;
                    }
                }
                // 检查嵌套表格
                for (XWPFTable nestedTable : cell.getTables()) {
                    if (containsPlaceholderInTable(nestedTable, placeholder)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    // ==================== 常量定义 ====================

    /**
     * 常用MIME类型
     */
    public static final class MimeTypes {
        public static final String WORD = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        public static final String PDF = "application/pdf";
        public static final String ZIP = "application/zip";
    }

    /**
     * 常用文件扩展名
     */
    public static final class Extensions {
        public static final String WORD = "docx";
        public static final String PDF = "pdf";
        public static final String ZIP = "zip";
    }
}
