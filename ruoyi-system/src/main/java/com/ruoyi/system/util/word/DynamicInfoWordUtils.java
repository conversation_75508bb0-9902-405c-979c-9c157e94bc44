package com.ruoyi.system.util.word;

import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STJc;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动态信息Word文档处理工具类
 */
@Slf4j
public class DynamicInfoWordUtils {

    /**
     * 生成动态信息Word文档
     *
     * @param templatePath  模板文件路径
     * @param data          动态信息数据
     * @param sealImagePath 公章图片路径
     * @return Word文档字节数组
     * @throws Exception 处理异常
     */
    public static byte[] generateDynamicInfoDocument(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        log.info("开始生成动态信息Word文档，模板路径: {}", templatePath);

        try (InputStream templateStream = getTemplateInputStream(templatePath)) {
            XWPFDocument document = new XWPFDocument(templateStream);

            // 调试：检查文档中的图片数量
            log.info("模板文档中的图片数量: {}", document.getAllPictures().size());

            // 1. 构建占位符参数
            Map<String, String> params = buildPlaceholderParams(data);

            // 2. 替换文档中的占位符
            replaceInDocumentBody(document, params);
            processParagraphsAndTables(document, params);

            // 3. 填充表格数据（仅动态信息）
            fillDynamicTableData(document, data);

            // 4. 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            log.info("动态信息Word文档生成完成");
            return outputStream.toByteArray();

        } catch (Exception e) {
            log.error("生成动态信息Word文档失败", e);
            throw new Exception("生成Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取模板输入流
     */
    private static InputStream getTemplateInputStream(String templatePath) throws Exception {
        try {
            log.info("尝试加载模板文件: {}", templatePath);

            // 尝试从类路径加载
            InputStream stream = DynamicInfoWordUtils.class.getClassLoader().getResourceAsStream(templatePath);
            if (stream != null) {
                log.info("✅ 从类路径成功加载模板文件: {}", templatePath);
                return stream;
            } else {
                log.warn("❌ 从类路径加载模板文件失败: {}", templatePath);
            }

            // 尝试从文件系统加载
            if (Files.exists(Paths.get(templatePath))) {
                log.info("✅ 从文件系统成功加载模板文件: {}", templatePath);
                return Files.newInputStream(Paths.get(templatePath));
            } else {
                log.warn("❌ 文件系统中不存在模板文件: {}", templatePath);
            }

            throw new FileNotFoundException("模板文件未找到: " + templatePath);
        } catch (Exception e) {
            log.error("❌ 加载模板文件失败: {}", templatePath, e);
            throw new Exception("加载模板文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建占位符参数
     */
    private static Map<String, String> buildPlaceholderParams(WordMeteorologicalVo data) {
        Map<String, String> params = new HashMap<>();

        // 基本信息（不包含${}，在替换时会自动添加）
        params.put("aircraftType", data.getAircraftType() != null ? data.getAircraftType() : "");
        params.put("registrationNumber", data.getRegistrationNumber() != null ? data.getRegistrationNumber() : "");
        params.put("flightDate", data.getFlightDate() != null ? data.getFlightDate() : "");
        params.put("fuel", data.getFuel() != null ? data.getFuel() : "");

        // 统计信息（不包含${}，在替换时会自动添加）
        params.put("groundTimeMinTotal", data.getGroundTimeMinTotal() != null ? data.getGroundTimeMinTotal() : "");
        params.put("airTimeMinTotal", data.getAirTimeMinTotal() != null ? data.getAirTimeMinTotal() : "");
        params.put("totalTimeMinTotal", data.getTotalTimeMinTotal() != null ? data.getTotalTimeMinTotal() : "");
        params.put("sortieCountTotal", data.getSortieCountTotal() != null ? data.getSortieCountTotal() : "");

        log.info("构建占位符参数完成，参数数量: {}", params.size());
        return params;
    }

    /**
     * 替换文档正文中的占位符
     */
    private static void replaceInDocumentBody(XWPFDocument document, Map<String, String> params) {
        log.info("开始替换文档正文中的占位符");
        
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceParagraphText(paragraph, params);
        }
        
        log.info("文档正文占位符替换完成");
    }

    /**
     * 处理段落和表格中的占位符
     */
    private static void processParagraphsAndTables(XWPFDocument document, Map<String, String> params) {
        log.info("开始处理段落和表格中的占位符");
        
        // 处理表格中的占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceParagraphText(paragraph, params);
                    }
                }
            }
        }
        
        log.info("段落和表格占位符处理完成");
    }

    /**
     * 替换段落文本
     */
    private static void replaceParagraphText(XWPFParagraph paragraph, Map<String, String> params) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) return;

        for (XWPFRun run : runs) {
            String text = run.getText(0);
            if (text == null || text.isEmpty()) continue;

            boolean replaced = false;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                if (text.contains(placeholder)) {
                    text = text.replace(placeholder, entry.getValue());
                    replaced = true;
                }
            }

            if (replaced) {
                run.setText(text, 0);
                // 设置字体样式
                run.setFontFamily("宋体");
                run.setFontSize(9);
                run.setBold(false);
                run.setColor("000000");
            }
        }
    }

    /**
     * 填充动态信息表格数据
     */
    private static void fillDynamicTableData(XWPFDocument document, WordMeteorologicalVo data) {
        log.info("开始填充动态信息表格数据");

        List<XWPFTable> tables = document.getTables();
        if (tables.isEmpty()) {
            log.warn("文档中没有找到表格");
            return;
        }

        // 处理第一个表格（主表格）
        XWPFTable mainTable = tables.get(0);

        // 分析表格结构，找到动态信息表头的位置
        DynamicTableHeaderInfo headerInfo = analyzeDynamicTableHeaders(mainTable);

        // 根据表头位置填充动态信息数据
        fillDynamicDataByHeaders(mainTable, headerInfo, data);

        log.info("动态信息表格数据填充完成");
    }

    /**
     * 动态信息表头信息结构
     */
    private static class DynamicTableHeaderInfo {
        int dynamicInfoHeaderRow = -1;       // 动态信息表头行
        int totalRow = -1;                   // 合计行
    }

    /**
     * 分析动态信息表格表头
     */
    private static DynamicTableHeaderInfo analyzeDynamicTableHeaders(XWPFTable table) {
        log.info("开始分析动态信息表格表头");
        
        DynamicTableHeaderInfo headerInfo = new DynamicTableHeaderInfo();
        List<XWPFTableRow> rows = table.getRows();
        
        for (int i = 0; i < rows.size(); i++) {
            XWPFTableRow row = rows.get(i);
            String rowText = getRowText(row);
            
            // 查找动态信息表头
            if (rowText.contains("批次") && rowText.contains("始发地") && rowText.contains("目的地") && rowText.contains("开车时刻")) {
                headerInfo.dynamicInfoHeaderRow = i;
                log.info("找到动态信息表头，行号: {}", i);
            }
            
            // 查找合计行
            if (rowText.contains("合计")) {
                headerInfo.totalRow = i;
                log.info("找到合计行，行号: {}", i);
            }
        }
        
        log.info("动态信息表格表头分析完成");
        return headerInfo;
    }

    /**
     * 获取行文本
     */
    private static String getRowText(XWPFTableRow row) {
        StringBuilder text = new StringBuilder();
        for (XWPFTableCell cell : row.getTableCells()) {
            text.append(cell.getText()).append(" ");
        }
        return text.toString();
    }

    /**
     * 根据表头位置填充动态信息数据
     */
    private static void fillDynamicDataByHeaders(XWPFTable table, DynamicTableHeaderInfo headerInfo, WordMeteorologicalVo data) {
        log.info("开始根据表头位置填充动态信息数据");

        // 填充动态信息
        if (headerInfo.dynamicInfoHeaderRow != -1 && data.getDynamicInfoList() != null && !data.getDynamicInfoList().isEmpty()) {
            log.info("开始填充动态信息，数据条数: {}", data.getDynamicInfoList().size());
            
            int insertPosition = headerInfo.dynamicInfoHeaderRow + 1;
            for (int i = 0; i < data.getDynamicInfoList().size(); i++) {
                WordFlightWeatherDynamicVo dynamic = data.getDynamicInfoList().get(i);
                log.info("处理动态信息第{}条: 批次={}, 始发地={}, 目的地={}, 开车时刻={}",
                        i + 1, dynamic.getBatch(), dynamic.getDepartureLocation(),
                        dynamic.getArrivalLocation(), dynamic.getCarStartTime());

                String[] rowData = buildDynamicRowData(dynamic);
                insertRowAtPosition(table, insertPosition, rowData);
                insertPosition++;
            }
        }

        log.info("根据表头位置填充动态信息数据完成");
    }

    /**
     * 构建动态信息行数据
     */
    private static String[] buildDynamicRowData(WordFlightWeatherDynamicVo dynamic) {
        return new String[]{
            dynamic.getBatch() != null ? dynamic.getBatch() : "",
            dynamic.getDepartureLocation() != null ? dynamic.getDepartureLocation() : "",
            dynamic.getArrivalLocation() != null ? dynamic.getArrivalLocation() : "",
            dynamic.getCarStartTime() != null ? dynamic.getCarStartTime() : "",
            dynamic.getTakeOffTime() != null ? dynamic.getTakeOffTime() : "",
            dynamic.getLandingTime() != null ? dynamic.getLandingTime() : "",
            dynamic.getCarStopTime() != null ? dynamic.getCarStopTime() : "",
            dynamic.getGroundTimeMin() != null ? dynamic.getGroundTimeMin() : "",
            dynamic.getAirTimeMin() != null ? dynamic.getAirTimeMin() : "",
            dynamic.getTotalTimeMin() != null ? dynamic.getTotalTimeMin() : "",
            dynamic.getSortieCount() != null ? dynamic.getSortieCount() : ""
        };
    }

    /**
     * 在指定位置插入行
     */
    private static void insertRowAtPosition(XWPFTable table, int position, String[] data) {
        log.info("在位置{}插入行，数据长度: {}", position, data.length);
        
        XWPFTableRow newRow = table.insertNewTableRow(position);
        List<XWPFTableCell> cells = newRow.getTableCells();
        
        // 确保有足够的单元格
        while (cells.size() < data.length) {
            newRow.addNewTableCell();
            cells = newRow.getTableCells();
        }
        
        // 填充数据
        for (int i = 0; i < data.length && i < cells.size(); i++) {
            XWPFTableCell cell = cells.get(i);

            // 清除单元格原有内容
            cell.removeParagraph(0);
            XWPFParagraph paragraph = cell.addParagraph();

            // 设置段落居中对齐
            paragraph.setAlignment(ParagraphAlignment.CENTER);

            XWPFRun run = paragraph.createRun();
            run.setText(data[i]);

            // 设置单元格垂直居中对齐
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        }
        
        log.info("数据插入完成，共填充{}列", Math.min(data.length, cells.size()));
    }

    /**
     * 生成文件名
     */
    public static String generateFileName(String prefix, String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return prefix + "_" + timestamp + "." + extension;
    }

    /**
     * 设置Word响应头
     */
    public static void setWordResponseHeaders(HttpServletResponse response, String fileName) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setCharacterEncoding("UTF-8");
            
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
            
            log.info("设置Word响应头完成，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("设置Word响应头失败", e);
        }
    }

    /**
     * 输出Word文档到响应流
     */
    public static void streamWordDocumentToResponse(HttpServletResponse response, byte[] docBytes) {
        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(docBytes);
            outputStream.flush();
            log.info("Word文档输出到响应流完成，大小: {} 字节", docBytes.length);
        } catch (Exception e) {
            log.error("输出Word文档到响应流失败", e);
        }
    }

    /**
     * 插入公章图片
     */
    private static void insertSealImage(XWPFDocument document, String sealImagePath) {
        log.info("开始插入公章图片，路径: {}", sealImagePath);

        try {
            // 获取公章图片输入流
            InputStream imageStream = getSealImageInputStream(sealImagePath);
            if (imageStream == null) {
                log.warn("公章图片未找到，跳过插入: {}", sealImagePath);
                return;
            }

            boolean imageInserted = false;

            // 可能的公章占位符格式
            String[] sealPlaceholders = {"${公章}", "${seal}", "${SEAL}", "公章", "[公章]", "【公章】"};

            // 查找包含公章占位符的段落
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                String text = paragraph.getText();
                if (text != null) {
                    for (String placeholder : sealPlaceholders) {
                        if (text.contains(placeholder)) {
                            log.info("找到公章占位符: {} 在段落中", placeholder);

                            // 清除原有内容
                            for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                                paragraph.removeRun(i);
                            }

                            // 创建新的Run并插入图片
                            XWPFRun run = paragraph.createRun();
                            run.addPicture(imageStream, XWPFDocument.PICTURE_TYPE_PNG, "公章.png",
                                         Units.toEMU(80), Units.toEMU(80)); // 80x80像素

                            log.info("公章图片插入成功");
                            imageInserted = true;
                            break;
                        }
                    }
                    if (imageInserted) break;
                }
            }

            // 如果段落中没有找到，检查表格中的公章占位符
            if (!imageInserted) {
                for (XWPFTable table : document.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                String text = paragraph.getText();
                                if (text != null) {
                                    for (String placeholder : sealPlaceholders) {
                                        if (text.contains(placeholder)) {
                                            log.info("找到公章占位符: {} 在表格中", placeholder);

                                            // 清除原有内容
                                            for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                                                paragraph.removeRun(i);
                                            }

                                            // 创建新的Run并插入图片
                                            XWPFRun run = paragraph.createRun();
                                            run.addPicture(imageStream, XWPFDocument.PICTURE_TYPE_PNG, "公章.png",
                                                         Units.toEMU(80), Units.toEMU(80)); // 80x80像素

                                            log.info("公章图片插入成功（表格中）");
                                            imageInserted = true;
                                            break;
                                        }
                                    }
                                    if (imageInserted) break;
                                }
                            }
                            if (imageInserted) break;
                        }
                        if (imageInserted) break;
                    }
                    if (imageInserted) break;
                }
            }

            if (!imageInserted) {
                log.warn("未找到公章占位符，跳过公章图片插入");
            }

        } catch (Exception e) {
            log.error("插入公章图片失败", e);
        }
    }

    /**
     * 获取公章图片输入流
     */
    private static InputStream getSealImageInputStream(String sealImagePath) throws Exception {
        try {
            // 尝试从类路径加载
            InputStream stream = DynamicInfoWordUtils.class.getClassLoader().getResourceAsStream(sealImagePath);
            if (stream != null) {
                log.info("从类路径加载公章图片: {}", sealImagePath);
                return stream;
            }

            // 尝试从文件系统加载
            if (Files.exists(Paths.get(sealImagePath))) {
                log.info("从文件系统加载公章图片: {}", sealImagePath);
                return Files.newInputStream(Paths.get(sealImagePath));
            }

            log.warn("公章图片未找到: {}", sealImagePath);
            return null;
        } catch (Exception e) {
            log.error("加载公章图片失败: {}", sealImagePath, e);
            return null;
        }
    }
}
